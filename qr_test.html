<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QR Scanner Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .camera-container {
      position: relative;
      width: 100%;
      max-width: 400px;
      margin: 20px auto;
      border: 2px solid #E53512;
      border-radius: 8px;
      overflow: hidden;
      background: #000;
    }
    .camera-video {
      width: 100%;
      height: 300px;
      object-fit: cover;
    }
    .result {
      margin: 20px 0;
      padding: 10px;
      border-radius: 5px;
      background: #f0f0f0;
    }
    .error {
      background: #f8d7da;
      color: #721c24;
    }
    .success {
      background: #d4edda;
      color: #155724;
    }
    button {
      padding: 10px 20px;
      margin: 10px;
      border: none;
      border-radius: 5px;
      background: #E53512;
      color: white;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>QR Scanner Test</h1>
  <p>This page tests the QR code scanning functionality.</p>
  
  <div>
    <button onclick="startScanner()">Start Camera</button>
    <button onclick="stopScanner()">Stop Camera</button>
  </div>
  
  <div class="camera-container">
    <video id="camera-video" class="camera-video" playsinline></video>
  </div>
  
  <div id="result" class="result">
    Ready to scan QR codes...
  </div>

  <script type="module">
    import QrScanner from 'https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js';

    let qrScanner = null;
    const videoElement = document.getElementById('camera-video');
    const resultElement = document.getElementById('result');

    window.startScanner = async function() {
      try {
        resultElement.textContent = 'Initializing camera...';
        resultElement.className = 'result';

        if (!QrScanner.hasCamera()) {
          throw new Error('No camera found on this device');
        }

        qrScanner = new QrScanner(
          videoElement,
          result => {
            resultElement.textContent = `QR Code detected: ${result.data}`;
            resultElement.className = 'result success';
            console.log('QR Code:', result.data);
          },
          {
            onDecodeError: error => {
              console.log('Decode error (normal):', error);
            },
            highlightScanRegion: true,
            highlightCodeOutline: true,
          }
        );

        await qrScanner.start();
        resultElement.textContent = 'Camera ready - point at QR code to scan';
        resultElement.className = 'result success';

      } catch (error) {
        console.error('Camera error:', error);
        let errorMessage = 'Failed to access camera: ';
        
        if (error.name === 'NotAllowedError') {
          errorMessage += 'Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage += 'Camera not supported in this browser.';
        } else {
          errorMessage += error.message || 'Unknown error occurred.';
        }
        
        resultElement.textContent = errorMessage;
        resultElement.className = 'result error';
      }
    };

    window.stopScanner = function() {
      if (qrScanner) {
        qrScanner.stop();
        qrScanner.destroy();
        qrScanner = null;
        resultElement.textContent = 'Camera stopped.';
        resultElement.className = 'result';
      }
    };

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      if (qrScanner) {
        qrScanner.stop();
        qrScanner.destroy();
      }
    });
  </script>
</body>
</html>
