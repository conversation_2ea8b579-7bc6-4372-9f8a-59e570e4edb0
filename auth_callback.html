<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Authentication</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 2rem;
      background: #f7f7f7;
      text-align: center;
    }
    .loader {
      border: 5px solid #f3f3f3;
      border-top: 5px solid #E53512;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 2rem auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .error {
      color: #E53512;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>Processing Authentication...</h1>
  <div class="loader"></div>
  <div id="message"></div>

  <script>
    // Parse the access token from the URL fragment
    function parseAccessToken() {
      const hash = window.location.hash.substring(1);
      const params = new URLSearchParams(hash);
      return params.get('access_token');
    }

    // Fetch user info from Google
    async function getUserInfo(accessToken) {
      try {
        const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user info');
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching user info:', error);
        throw error;
      }
    }

    // Main function to handle authentication
    async function handleAuthentication() {
      try {
        const accessToken = parseAccessToken();

        if (!accessToken) {
          document.getElementById('message').innerHTML = '<p class="error">Authentication failed. No access token found.</p>';
          return;
        }

        const userInfo = await getUserInfo(accessToken);

        // Get the authorized emails from sessionStorage
        const authorizedEmailsStr = sessionStorage.getItem('authorizedEmail');
        const allowedEmailsStr = sessionStorage.getItem('allowedEmails');

        // Parse allowed emails - try new format first, fallback to old format
        let allowedEmails = [];
        if (allowedEmailsStr) {
          try {
            allowedEmails = JSON.parse(allowedEmailsStr);
          } catch (e) {
            console.warn('Failed to parse allowedEmails, falling back to authorizedEmail');
          }
        }

        // Fallback to old format if new format not available
        if (allowedEmails.length === 0 && authorizedEmailsStr) {
          allowedEmails = authorizedEmailsStr.split(',').map(email => email.trim());
        }

        // Check if the user is authorized
        const isAuthorized = allowedEmails.includes(userInfo.email);
        if (isAuthorized) {
          // Store authentication info in localStorage
          localStorage.setItem('snownavi_auth', JSON.stringify({
            email: userInfo.email,
            name: userInfo.name,
            picture: userInfo.picture,
            accessToken: accessToken,
            expiresAt: Date.now() + 3600000 // Token expires in 1 hour
          }));

          // Redirect to admin page
          window.location.href = 'admin.html';
        } else {
          document.getElementById('message').innerHTML = `
            <p class="error">Access denied. Only authorized administrators can access this page.</p>
            <p>Your email: ${userInfo.email} is not authorized.</p>
            <p><a href="login.html">Back to login</a></p>
          `;
        }
      } catch (error) {
        document.getElementById('message').innerHTML = `<p class="error">Authentication error: ${error.message}</p>`;
      }
    }

    // Run the authentication handler when the page loads
    window.onload = handleAuthentication;
  </script>
</body>
</html>
