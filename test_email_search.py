#!/usr/bin/env python3
"""
Test script for email search functionality
"""

import json
import os

def test_email_search():
    """Test the email search logic"""
    
    # Load members data
    members_file = 'data/members.json'
    if not os.path.exists(members_file):
        print("Members file not found!")
        return
    
    with open(members_file, 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    # Test email search
    test_email = "<EMAIL>"
    print(f"Searching for email: {test_email}")
    
    found = False
    for member_id, member_data in members.items():
        if 'email' in member_data and member_data['email'].strip().lower() == test_email.lower():
            print(f"Found member: {member_id} - {member_data.get('name', 'Unknown')}")
            found = True
            break
    
    if not found:
        print("No member found with this email")
    
    # Test another email
    test_email2 = "<EMAIL>"
    print(f"\nSearching for email: {test_email2}")
    
    found = False
    for member_id, member_data in members.items():
        if 'email' in member_data and member_data['email'].strip().lower() == test_email2.lower():
            print(f"Found member: {member_id} - {member_data.get('name', 'Unknown')}")
            found = True
            break
    
    if not found:
        print("No member found with this email")

if __name__ == "__main__":
    test_email_search()
